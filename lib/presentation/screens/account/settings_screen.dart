import 'package:arabic_sign_language/bloc/Theme/themebloc_bloc.dart';
import 'package:arabic_sign_language/bloc/language/language_bloc.dart';
import 'package:arabic_sign_language/bloc/settings/settings_bloc.dart';
import 'package:arabic_sign_language/bloc/settings/settings_event.dart';
import 'package:arabic_sign_language/bloc/settings/settings_state.dart';
import 'package:arabic_sign_language/common/router.dart';

import 'package:arabic_sign_language/presentation/core/constants.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter/widgets.dart' as ui;
import 'package:package_info_plus/package_info_plus.dart';

import '../../../bloc/auth/auth_bloc.dart';
import '../home/<USER>';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  String version = '';

  Future<void> getVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      version = packageInfo.version;
    });
  }

  @override
  void initState() {
    super.initState();
    // Use the provided SettingsBloc instead of creating a new one
    context.read<SettingsBloc>().add(LoadSettings());
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await getVersion();
    });
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Stack(
        children: [
          Container(
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage(APP_BG),
                fit: BoxFit.cover,
              ),
            ),
            child: SafeArea(
              child: Column(
                children: [
                  _topBar(context),
                  const SizedBox(height: 20),
                  Expanded(child: _contentArea(size)),
                  const SizedBox(height: 85),
                ],
              ),
            ),
          ),
          BlocBuilder<SettingsBloc, SettingsState>(
            builder: (context, state) {
              if (state is SettingsLoading || state is SettingsSaving) {
                return Container(
                  color: Colors.black.withOpacity(0.6),
                  child: const Center(
                    child: SizedBox(),
                  ),
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
    );
  }

  Widget _topBar(BuildContext context) {
    final isRtl = Directionality.of(context) == ui.TextDirection.rtl;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Row(
        children: [
          IconButton(
            onPressed: () {
              bool isMatch = currentNavItems.value.any(
                (item) => item.title.toLowerCase() == "settings".toLowerCase(),
              );
              int index = currentNavItems.value.indexWhere(
                (item) => item.title.toLowerCase() == "settings".toLowerCase(),
              );
              if (isMatch && index <= 3 && bottomBarIndex.value == 4) {
                Navigator.of(context).pop();
              } else if (isMatch && index <= 3 && bottomBarIndex.value != 0) {
                bottomBarIndex.value = 0;
              } else if (bottomBarIndex.value == 4) {
                bottomBarIndex.value = 0;
                context.pop();
              } else {
                context.pop();
              }
            },
            icon: Image.asset(BACK_BUTTON, width: 24, height: 24),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              'settings'.tr(),
              style: Theme.of(context).textTheme.displayLarge?.copyWith(
                    fontSize: 24,
                    fontWeight: FontWeight.w600,
                  ),
              textAlign: isRtl ? TextAlign.right : TextAlign.left,
            ),
          ),
        ],
      ),
    );
  }

  Widget _contentArea(Size size) {
    return BlocListener<LanguageBloc, LanguageState>(
      listener: (context, languageState) {
        // Update app's locale when LanguageBloc state changes
        context.setLocale(languageState.locale);
        // Force rebuild to reflect language change
        setState(() {});
      },
      child: BlocConsumer<SettingsBloc, SettingsState>(
        listener: (context, state) {
          if (state is SettingsError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          } else if (state is SettingsSaved) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.green,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is SettingsLoaded) {
            return _buildSettingsContent(state);
          }
          return const Center(
            child: CircularProgressIndicator(),
          );
        },
      ),
    );
  }

  Widget _buildSettingsContent(SettingsLoaded state) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color.fromARGB(150, 128, 103, 255),
            Color.fromARGB(200, 30, 20, 90)
          ],
        ),
        border: Border.all(color: Colors.white24, width: 1.2),
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSettingsMenuItem(Icons.tune, 'preferences'.tr(), onTap: () {
              _showPreferencesScreen();
            }),
            const SizedBox(height: 12),
            _buildSettingsMenuItem(Icons.person_outline, 'profile'.tr(),
                onTap: () {
              context.pushNamed(AppRoutes.PROFILE_ROUTE_NAME, extra: true);
            }),
            const SizedBox(height: 12),
            _buildSettingsMenuItem(Icons.star_outline, 'subscription'.tr(),
                onTap: () {
              context.pushNamed(AppRoutes.SUBSCRIPTION_ROUTE_NAME, extra: true);
            }),
            const SizedBox(height: 12),
            _buildSettingsMenuItem(Icons.help_outline, 'help_and_support'.tr(),
                onTap: () {
              context.pushNamed(AppRoutes.HELP_SUPPORT_ROUTE_NAME, extra: true);
            }),
            const SizedBox(height: 12),
            _buildSettingsMenuItem(Icons.privacy_tip, 'privacy_policy'.tr(),
                onTap: () {
              context.pushNamed(AppRoutes.TERMS_POLICIES_ROUTE_NAME);
            }),
            const SizedBox(height: 12),
            _buildSettingsMenuItem(Icons.description, 'terms_of_service'.tr(),
                onTap: () {
              context.pushNamed(AppRoutes.TERMS_POLICIES_ROUTE_NAME);
            }),
            const SizedBox(height: 12),
            _buildSettingsMenuItem(Icons.info_outline, 'about'.tr(), onTap: () {
              _showAboutDialog();
            }),
            const SizedBox(height: 12),
            _buildSettingsMenuItem(Icons.logout, 'logout'.tr(), onTap: () {
              _confirmLogout(context);
            }),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsMenuItem(IconData icon, String title,
      {VoidCallback? onTap}) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: ListTile(
        leading: Icon(icon, color: Colors.white, size: 24),
        title: Text(
          title,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          color: Colors.white.withOpacity(0.7),
          size: 16,
        ),
        onTap: onTap,
      ),
    );
  }

  void _showPreferencesScreen() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.8,
          decoration: const BoxDecoration(
            color: Color(0XFF221B67),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: BlocBuilder<SettingsBloc, SettingsState>(
            builder: (context, state) {
              if (state is SettingsLoaded) {
                return _buildPreferencesContent(state);
              }
              return const Center(child: CircularProgressIndicator());
            },
          ),
        );
      },
    );
  }

  Widget _buildPreferencesContent(SettingsLoaded state) {
    return BlocListener<LanguageBloc, LanguageState>(
      listener: (context, languageState) {
        // Update app's locale when LanguageBloc state changes
        context.setLocale(languageState.locale);
        // Force rebuild to reflect language change
        setState(() {});
      },
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'preferences'.tr(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close, color: Colors.white),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    _buildLanguageSelector(state),
                    // const SizedBox(height: 16),
                    _buildThemeSelector(),
                    // const SizedBox(height: 16),
                    _buildNotificationsSection(state),
                    // const SizedBox(height: 16),
                    _buildFontSizeController(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFontSizeController() {
    return BlocBuilder<SettingsBloc, SettingsState>(
      builder: (context, state) {
        if (state is! SettingsLoaded) return const SizedBox();

        double currentSize = state.textSize;
        return Container(
            margin: const EdgeInsets.only(bottom: 8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.white.withOpacity(0.2)),
            ),
            child: ExpansionTile(
              leading:
                  const Icon(Icons.text_fields, color: Colors.white, size: 24),
              title: const Text(
                'Font Size',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              children: [
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Slider(
                        value: currentSize,
                        min: 0.8,
                        max: 1.6,
                        divisions: 8,
                        label: currentSize.toStringAsFixed(2),
                        activeColor: Colors.white,
                        inactiveColor: Colors.white.withOpacity(0.3),
                        onChanged: (value) {
                          // Debug print to verify event is being dispatched
                          print('Font size slider changed to: $value');
                          // Dispatch event to SettingsBloc
                          context
                              .read<SettingsBloc>()
                              .add(ChangeTextSize(value));
                        },
                      ),
                      Text(
                        'Current: ${currentSize.toStringAsFixed(2)}x',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.7),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ));
      },
    );
  }

  Widget _buildLanguageSelector(SettingsLoaded state) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: ListTile(
        leading: const Icon(Icons.language, color: Colors.white, size: 24),
        title: const Text(
          'Language',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(
          state.language.languageCode == 'en' ? 'EN' : 'AR',
          style: TextStyle(
            color: Colors.white.withOpacity(0.7),
            fontSize: 14,
          ),
        ),
        trailing: DropdownButton<String>(
          value: state.language.languageCode,
          dropdownColor: Colors.grey[800],
          style: const TextStyle(color: Colors.white),
          underline: Container(),
          icon: const Icon(Icons.arrow_drop_down, color: Colors.white),
          items: const [
            DropdownMenuItem(value: 'en', child: Text('English')),
            DropdownMenuItem(value: 'ar', child: Text('العربية')),
          ],
          onChanged: (String? newValue) {
            if (newValue != null) {
              final newLocale = Locale(newValue);
              // Update SettingsBloc
              context.read<SettingsBloc>().add(ChangeLanguage(newLocale));
              // Update app's locale context
              context.setLocale(newLocale);
              // Force rebuild to reflect language change
              setState(() {});
            }
          },
        ),
      ),
    );
  }

  Widget _buildThemeSelector() {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        final themeMode = state.themeMode;

        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.white.withOpacity(0.2)),
          ),
          child: ListTile(
            leading:
                const Icon(Icons.brightness_6, color: Colors.white, size: 24),
            title: const Text(
              'Theme',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            subtitle: Text(
              _getThemeName(themeMode),
              style: TextStyle(
                color: Colors.white.withOpacity(0.7),
                fontSize: 14,
              ),
            ),
            trailing: DropdownButton<ThemeMode>(
              value: themeMode,
              dropdownColor: Colors.grey[800],
              style: const TextStyle(color: Colors.white),
              underline: Container(),
              icon: const Icon(Icons.arrow_drop_down, color: Colors.white),
              items: const [
                DropdownMenuItem(
                    value: ThemeMode.system, child: Text('System')),
                DropdownMenuItem(value: ThemeMode.light, child: Text('Light')),
                DropdownMenuItem(value: ThemeMode.dark, child: Text('Dark')),
              ],
              onChanged: (ThemeMode? newValue) {
                if (newValue != null) {
                  context
                      .read<ThemeBloc>()
                      .add(ThemeChanged(mapAppThemeToFlutterTheme(newValue)));
                }
              },
            ),
          ),
        );
      },
    );
  }

  AppThemeMode mapAppThemeToFlutterTheme(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return AppThemeMode.light;
      case ThemeMode.dark:
        return AppThemeMode.dark;
      case ThemeMode.system:
      default:
        return AppThemeMode.system;
    }
  }

  String _getThemeName(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.light:
        return 'Light';
      case ThemeMode.dark:
        return 'Dark';
      default:
        return 'System';
    }
  }

  Widget _buildNotificationsSection(SettingsLoaded state) {
    return Column(
      children: [
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.white.withOpacity(0.2)),
          ),
          child: ListTile(
            leading: const Icon(Icons.notifications_outlined,
                color: Colors.white, size: 24),
            title: const Text(
              'Push Notifications',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            trailing: Switch(
              value: state.pushNotificationsEnabled,
              onChanged: (bool value) {
                context
                    .read<SettingsBloc>()
                    .add(TogglePushNotifications(value));
              },
              activeColor: Colors.blue,
            ),
          ),
        ),
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.white.withOpacity(0.2)),
          ),
          child: ListTile(
            leading:
                const Icon(Icons.email_outlined, color: Colors.white, size: 24),
            title: const Text(
              'Email Notifications',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            trailing: Switch(
              value: state.emailNotificationsEnabled,
              onChanged: (bool value) {
                context
                    .read<SettingsBloc>()
                    .add(ToggleEmailNotifications(value));
              },
              activeColor: Colors.blue,
            ),
          ),
        ),
      ],
    );
  }

  void _confirmLogout(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0XFF221B67),
        title: Text('confirm_logout'.tr()),
        content: Text('logout_confirmation_message'.tr()),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('cancel'.tr()),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              _performLogout(context);
            },
            child: Text(
              'logout'.tr(),
              style: const TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  void _performLogout(BuildContext context) {
    // Dispatch logout event to auth bloc
    context.read<AuthBloc>().add(LogoutRequested());

    // Navigate to login screen
    context.goNamed(AppRoutes.LOGIN_ROUTE_NAME);

    // Reset bottom navigation index
    bottomBarIndex.value = 0;
  }

  void _showAboutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0XFF221B67),
          title: const Text(
            'About Saudi Sign Language',
            style: TextStyle(color: Colors.white),
          ),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Center(
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      image: const DecorationImage(
                        image: AssetImage(APP_ICON),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                Center(
                  child: Text(
                    'app_name'.tr(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Center(
                  child: Text(
                    '${'version'.tr()}: $version',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 14,
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                Text(
                  'About'.tr(),
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'About_the_app_dec'.tr(),
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 14,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('close'.tr(),
                  style: const TextStyle(color: Colors.white)),
            ),
          ],
        );
      },
    );
  }
}
